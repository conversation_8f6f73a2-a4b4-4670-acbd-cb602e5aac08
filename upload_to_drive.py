from google.oauth2.service_account import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import os
import pickle
import logging
import time
from datetime import datetime
import json

# === CONFIG ===
# Choose authentication method: 'service_account' or 'oauth'
AUTH_METHOD = 'oauth'  # Using OAuth for regular Drive folders

# Service Account settings (for Shared Drives)
SERVICE_ACCOUNT_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\GoogleDriveUploadKey.json"

# OAuth settings (for regular Drive folders)
OAUTH_CREDENTIALS_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\GoogleUploadOAuthClient.json"  # Download from Google Cloud Console
TOKEN_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\token.pickle"

FOLDER_ID = '14tj48mOu51vjRTb53U_61sbGExsFKkZB'
LOCAL_FOLDER_PATH = r"D:\Automation Files\ROasis YGN Data Google Drive Upload"
SCOPES = ['https://www.googleapis.com/auth/drive']

# === LOGGING CONFIG ===
LOG_FILE = os.path.join(LOCAL_FOLDER_PATH, "upload_log.txt")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()  # Also print to console
    ]
)
logger = logging.getLogger(__name__)

def log_step(step_name, details=""):
    """Log a step with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    message = f"[{timestamp}] {step_name}"
    if details:
        message += f" - {details}"
    logger.info(message)
    return time.time()

def get_file_size_mb(filepath):
    """Get file size in MB"""
    size_bytes = os.path.getsize(filepath)
    size_mb = size_bytes / (1024 * 1024)
    return size_mb, size_bytes

def format_duration(seconds):
    """Format duration in human readable format"""
    if seconds < 60:
        return f"{seconds:.2f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.2f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.2f} hours"

def log_upload_summary(filename, file_size_mb, file_size_bytes, upload_start_time, duration, success, error_msg=""):
    """Log detailed upload summary"""
    status = "SUCCESS" if success else "FAILED"
    start_time_formatted = datetime.fromtimestamp(upload_start_time).strftime("%Y-%m-%d %H:%M:%S")

    summary = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "filename": filename,
        "start_upload_time": start_time_formatted,
        "file_size_mb": round(file_size_mb, 2),
        "file_size_bytes": file_size_bytes,
        "upload_duration_seconds": round(duration, 2),
        "upload_duration_formatted": format_duration(duration),
        "status": status,
        "local_path": os.path.join(LOCAL_FOLDER_PATH, filename),
        "target_folder_id": FOLDER_ID,
        "error_message": error_msg if not success else ""
    }

    logger.info(f"UPLOAD SUMMARY: {json.dumps(summary, indent=2)}")
    return summary

def log_session_summary(total_files_qty, session_start_time, successful_uploads, failed_uploads, skipped_files, upload_summaries):
    """Log comprehensive session summary with all required fields"""
    session_end_time = time.time()
    total_duration = session_end_time - session_start_time

    # Calculate total download/upload statistics
    total_download_size_bytes = 0
    total_download_size_mb = 0
    total_upload_duration = 0

    for summary in upload_summaries:
        if summary['status'] == 'SUCCESS':
            total_download_size_bytes += summary['file_size_bytes']
            total_download_size_mb += summary['file_size_mb']
            total_upload_duration += summary['upload_duration_seconds']

    session_summary = {
        "session_start_time": datetime.fromtimestamp(session_start_time).strftime("%Y-%m-%d %H:%M:%S"),
        "session_end_time": datetime.fromtimestamp(session_end_time).strftime("%Y-%m-%d %H:%M:%S"),
        "total_file_qty": total_files_qty,
        "successful_uploads": successful_uploads,
        "failed_uploads": failed_uploads,
        "skipped_files": skipped_files,
        "total_download_size_mb": round(total_download_size_mb, 2),
        "total_download_size_bytes": total_download_size_bytes,
        "total_upload_duration_seconds": round(total_upload_duration, 2),
        "total_upload_duration_formatted": format_duration(total_upload_duration),
        "total_session_duration_seconds": round(total_duration, 2),
        "total_session_duration_formatted": format_duration(total_duration),
        "average_upload_speed_mbps": round((total_download_size_mb / total_upload_duration), 2) if total_upload_duration > 0 else 0,
        "local_folder_path": LOCAL_FOLDER_PATH,
        "target_folder_id": FOLDER_ID
    }

    logger.info(f"SESSION SUMMARY: {json.dumps(session_summary, indent=2)}")
    return session_summary

def authenticate():
    if AUTH_METHOD == 'oauth':
        return authenticate_oauth()
    else:
        return authenticate_service_account()

def authenticate_service_account():
    creds = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)
    return build('drive', 'v3', credentials=creds)

def authenticate_oauth():
    creds = None
    # Load existing token
    if os.path.exists(TOKEN_FILE):
        with open(TOKEN_FILE, 'rb') as token:
            creds = pickle.load(token)

    # If there are no valid credentials, get new ones
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            if not os.path.exists(OAUTH_CREDENTIALS_FILE):
                print(f"❌ OAuth credentials file not found: {OAUTH_CREDENTIALS_FILE}")
                print("Please download credentials.json from Google Cloud Console")
                return None

            flow = InstalledAppFlow.from_client_secrets_file(OAUTH_CREDENTIALS_FILE, SCOPES)
            creds = flow.run_local_server(port=0)

        # Save credentials for next run
        with open(TOKEN_FILE, 'wb') as token:
            pickle.dump(creds, token)

    return build('drive', 'v3', credentials=creds)

def get_existing_files(service):
    files_in_drive = {}
    page_token = None
    while True:
        response = service.files().list(
            q=f"'{FOLDER_ID}' in parents and trashed=false",
            fields="nextPageToken, files(id, name)",
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            corpora='allDrives',
            pageToken=page_token
        ).execute()

        for file in response.get('files', []):
            files_in_drive[file['name']] = file['id']
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
    return files_in_drive

def upload_bat_files(service, existing_files):
    upload_session_start = time.time()
    log_step("UPLOAD SESSION STARTED", f"Scanning directory: {LOCAL_FOLDER_PATH}")

    # Count total .bak files first
    all_files = os.listdir(LOCAL_FOLDER_PATH)
    total_bak_files = len([f for f in all_files if f.endswith(".bak")])

    log_step("FILE INVENTORY", f"Total .bak files found: {total_bak_files}")
    print(f"📁 Found {total_bak_files} .bak files to process")

    total_files_processed = 0
    successful_uploads = 0
    failed_uploads = 0
    skipped_files = 0
    upload_summaries = []

    for filename in all_files:
        if filename.endswith(".bak"):
            total_files_processed += 1
            filepath = os.path.join(LOCAL_FOLDER_PATH, filename)

            if filename not in existing_files:
                # Get file information
                file_size_mb, file_size_bytes = get_file_size_mb(filepath)

                log_step("UPLOAD STARTED", f"File: {filename} | Size: {file_size_mb:.2f} MB ({file_size_bytes:,} bytes) | File {total_files_processed}/{total_bak_files}")
                upload_start_time = time.time()

                file_metadata = {
                    'name': filename,
                    'parents': [FOLDER_ID]
                }

                try:
                    log_step("CREATING MEDIA UPLOAD", f"File: {filename}")
                    media = MediaFileUpload(filepath, resumable=False)

                    log_step("SENDING TO GOOGLE DRIVE", f"File: {filename}")
                    result = service.files().create(
                        body=file_metadata,
                        media_body=media,
                        fields='id',
                        supportsAllDrives=True
                    ).execute()

                    upload_duration = time.time() - upload_start_time
                    successful_uploads += 1

                    log_step("UPLOAD COMPLETED", f"File: {filename} | Drive ID: {result.get('id')} | Duration: {format_duration(upload_duration)}")
                    summary = log_upload_summary(filename, file_size_mb, file_size_bytes, upload_start_time, upload_duration, True)
                    upload_summaries.append(summary)

                    print(f"✅ Uploaded: {filename} (ID: {result.get('id')}) - {file_size_mb:.2f} MB in {format_duration(upload_duration)}")

                except Exception as e:
                    upload_duration = time.time() - upload_start_time
                    failed_uploads += 1
                    error_msg = str(e)

                    log_step("UPLOAD FAILED", f"File: {filename} | Error: {error_msg}")
                    summary = log_upload_summary(filename, file_size_mb, file_size_bytes, upload_start_time, upload_duration, False, error_msg)
                    upload_summaries.append(summary)

                    print(f"❌ Failed to upload {filename}: {error_msg}")

                    # Try to get more info about the folder
                    try:
                        folder_info = service.files().get(
                            fileId=FOLDER_ID,
                            fields='id,name,driveId,parents',
                            supportsAllDrives=True
                        ).execute()
                        log_step("FOLDER INFO", f"Folder details: {folder_info}")
                    except Exception as folder_error:
                        log_step("FOLDER INFO ERROR", f"Could not get folder info: {folder_error}")
            else:
                skipped_files += 1
                log_step("FILE SKIPPED", f"File: {filename} (already exists in Drive) | File {total_files_processed}/{total_bak_files}")
                print(f"⏭️ Skipped (already exists): {filename}")
        else:
            log_step("FILE SKIPPED", f"File: {filename} (not a .bak file)")
            print(f"⏭️ Skipped (not a .bak file): {filename}")

    # Log comprehensive session summary
    session_summary = log_session_summary(total_bak_files, upload_session_start, successful_uploads, failed_uploads, skipped_files, upload_summaries)

    log_step("UPLOAD SESSION COMPLETED", f"Processed {total_files_processed}/{total_bak_files} files")

    print(f"\n📊 Comprehensive Session Summary:")
    print(f"   📁 Total File QTY: {session_summary['total_file_qty']}")
    print(f"   🕐 Start Time: {session_summary['session_start_time']}")
    print(f"   📊 Total Download Size: {session_summary['total_download_size_mb']} MB ({session_summary['total_download_size_bytes']:,} bytes)")
    print(f"   ⏱️  Upload Duration: {session_summary['total_upload_duration_formatted']}")
    print(f"   🕐 Total Session Duration: {session_summary['total_session_duration_formatted']}")
    print(f"   ✅ Successful Uploads: {session_summary['successful_uploads']}")
    print(f"   ❌ Failed Uploads: {session_summary['failed_uploads']}")
    print(f"   ⏭️  Skipped Files: {session_summary['skipped_files']}")
    print(f"   🚀 Average Upload Speed: {session_summary['average_upload_speed_mbps']} MB/s")
    print(f"   📝 Log File: {LOG_FILE}")

def check_folder_details(service):
    """Check detailed information about the target folder"""
    try:
        folder_info = service.files().get(
            fileId=FOLDER_ID,
            fields='id,name,driveId,parents,capabilities,permissionIds,owners',
            supportsAllDrives=True
        ).execute()

        print(f"📁 Folder Name: {folder_info.get('name')}")
        print(f"📁 Folder ID: {folder_info.get('id')}")
        print(f"📁 Drive ID: {folder_info.get('driveId', 'None (Regular Drive)')}")
        print(f"📁 Parents: {folder_info.get('parents', 'None')}")
        print(f"📁 Capabilities: {folder_info.get('capabilities', {})}")

        # Check if we can write to this folder
        can_add_children = folder_info.get('capabilities', {}).get('canAddChildren', False)
        print(f"📁 Can Add Files: {can_add_children}")

        return folder_info.get('driveId') is not None, can_add_children

    except Exception as e:
        print(f"❌ Error checking folder: {str(e)}")
        return False, False

def main():
    # Start main process logging
    main_start_time = log_step("MAIN PROCESS STARTED", f"Script: {__file__}")

    print("🚀 Starting Google Drive upload process...")
    print(f"🔐 Using {AUTH_METHOD} authentication")
    print(f"📝 Logging to: {LOG_FILE}")

    log_step("AUTHENTICATION STARTED", f"Method: {AUTH_METHOD}")
    service = authenticate()
    if service is None:
        log_step("AUTHENTICATION FAILED", "Could not authenticate with Google Drive")
        print("❌ Authentication failed")
        return

    log_step("AUTHENTICATION SUCCESSFUL", f"Method: {AUTH_METHOD}")
    print("✅ Authentication successful")

    # Check folder details
    log_step("FOLDER VERIFICATION STARTED", f"Target folder ID: {FOLDER_ID}")
    is_shared_drive, can_add_files = check_folder_details(service)

    if not can_add_files:
        log_step("FOLDER VERIFICATION FAILED", "Cannot add files to target folder")
        print("❌ Cannot add files to this folder. Check permissions.")
        return

    if not is_shared_drive and AUTH_METHOD == 'service_account':
        log_step("COMPATIBILITY ERROR", "Service account cannot upload to regular Drive folder")
        print("❌ Service accounts cannot upload to regular Drive folders.")
        print("   Please use OAuth authentication or move folder to Shared Drive.")
        return
    elif not is_shared_drive:
        log_step("FOLDER VERIFICATION SUCCESSFUL", "OAuth can upload to regular Drive folder")
        print("✅ Using OAuth - can upload to regular Drive folders")
    else:
        log_step("FOLDER VERIFICATION SUCCESSFUL", "Target folder is in Shared Drive")

    log_step("SCANNING EXISTING FILES", f"Target folder ID: {FOLDER_ID}")
    existing_files = get_existing_files(service)
    log_step("EXISTING FILES FOUND", f"Count: {len(existing_files)} files")
    print(f"📋 Found {len(existing_files)} existing files in target folder")

    # Start upload process
    upload_bat_files(service, existing_files)

    # Log total process duration
    total_duration = time.time() - main_start_time
    log_step("MAIN PROCESS COMPLETED", f"Total duration: {format_duration(total_duration)}")
    print(f"\n🎉 Process completed in {format_duration(total_duration)}")
    print(f"📝 Detailed log saved to: {LOG_FILE}")

if __name__ == '__main__':
    main()
