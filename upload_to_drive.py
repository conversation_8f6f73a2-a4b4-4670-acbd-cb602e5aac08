from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import os

# === CONFIG ===
SERVICE_ACCOUNT_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\elite-pointer-465509-m8-7c938d418294.json"
FOLDER_ID = '14tj48mOu51vjRTb53U_61sbGExsFKkZB'
LOCAL_FOLDER_PATH = r"D:\Automation Files\ROasis YGN Data Google Drive Upload"
SCOPES = ['https://www.googleapis.com/auth/drive']

def authenticate():
    creds = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)
    return build('drive', 'v3', credentials=creds)

def get_existing_files(service):
    files_in_drive = {}
    page_token = None
    while True:
        response = service.files().list(
            q=f"'{FOLDER_ID}' in parents and trashed=false",
            fields="nextPageToken, files(id, name)",
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            corpora='allDrives',
            pageToken=page_token
        ).execute()

        for file in response.get('files', []):
            files_in_drive[file['name']] = file['id']
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
    return files_in_drive

def upload_bat_files(service, existing_files):
    for filename in os.listdir(LOCAL_FOLDER_PATH):
        if filename.endswith(".bak") and filename not in existing_files:
            filepath = os.path.join(LOCAL_FOLDER_PATH, filename)
            print(f"Uploading: {filename}")
            file_metadata = {
                'name': filename,
                'parents': [FOLDER_ID]
            }
            media = MediaFileUpload(filepath, resumable=True)
            service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id',
                supportsAllDrives=True
            ).execute()
            print(f"✅ Uploaded: {filename}")
        else:
            print(f"⏭️ Skipped (already exists): {filename}")

def main():
    service = authenticate()
    existing_files = get_existing_files(service)
    upload_bat_files(service, existing_files)

if __name__ == '__main__':
    main()
