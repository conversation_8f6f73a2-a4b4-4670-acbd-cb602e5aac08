from google.oauth2.service_account import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import os
import pickle

# === CONFIG ===
# Choose authentication method: 'service_account' or 'oauth'
AUTH_METHOD = 'oauth'  # Change to 'service_account' if you want to use service account

# Service Account settings (for Shared Drives)
SERVICE_ACCOUNT_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\GoogleDriveUploadKey.json"

# OAuth settings (for regular Drive folders)
OAUTH_CREDENTIALS_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\GoogleUploadOAuthClient.json"  # Download from Google Cloud Console
TOKEN_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\token.pickle"

FOLDER_ID = '14tj48mOu51vjRTb53U_61sbGExsFKkZB'
LOCAL_FOLDER_PATH = r"D:\Automation Files\ROasis YGN Data Google Drive Upload"
SCOPES = ['https://www.googleapis.com/auth/drive']

def authenticate():
    if AUTH_METHOD == 'oauth':
        return authenticate_oauth()
    else:
        return authenticate_service_account()

def authenticate_service_account():
    creds = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)
    return build('drive', 'v3', credentials=creds)

def authenticate_oauth():
    creds = None
    # Load existing token
    if os.path.exists(TOKEN_FILE):
        with open(TOKEN_FILE, 'rb') as token:
            creds = pickle.load(token)

    # If there are no valid credentials, get new ones
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            if not os.path.exists(OAUTH_CREDENTIALS_FILE):
                print(f"❌ OAuth credentials file not found: {OAUTH_CREDENTIALS_FILE}")
                print("Please download credentials.json from Google Cloud Console")
                return None

            flow = InstalledAppFlow.from_client_secrets_file(OAUTH_CREDENTIALS_FILE, SCOPES)
            creds = flow.run_local_server(port=0)

        # Save credentials for next run
        with open(TOKEN_FILE, 'wb') as token:
            pickle.dump(creds, token)

    return build('drive', 'v3', credentials=creds)

def get_existing_files(service):
    files_in_drive = {}
    page_token = None
    while True:
        response = service.files().list(
            q=f"'{FOLDER_ID}' in parents and trashed=false",
            fields="nextPageToken, files(id, name)",
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            corpora='allDrives',
            pageToken=page_token
        ).execute()

        for file in response.get('files', []):
            files_in_drive[file['name']] = file['id']
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
    return files_in_drive

def upload_bat_files(service, existing_files):
    for filename in os.listdir(LOCAL_FOLDER_PATH):
        if filename.endswith(".bak") and filename not in existing_files:
            filepath = os.path.join(LOCAL_FOLDER_PATH, filename)
            print(f"Uploading: {filename}")
            file_metadata = {
                'name': filename,
                'parents': [FOLDER_ID]
            }
            media = MediaFileUpload(filepath, resumable=True)
            try:
                result = service.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id',
                    supportsAllDrives=True
                ).execute()
                print(f"✅ Uploaded: {filename} (ID: {result.get('id')})")
            except Exception as e:
                print(f"❌ Failed to upload {filename}: {str(e)}")
                # Try to get more info about the folder
                try:
                    folder_info = service.files().get(
                        fileId=FOLDER_ID,
                        fields='id,name,driveId,parents',
                        supportsAllDrives=True
                    ).execute()
                    print(f"Folder info: {folder_info}")
                except Exception as folder_error:
                    print(f"Could not get folder info: {folder_error}")
        elif filename.endswith(".bak"):
            print(f"⏭️ Skipped (already exists): {filename}")
        else:
            print(f"⏭️ Skipped (not a .bak file): {filename}")

def check_folder_details(service):
    """Check detailed information about the target folder"""
    try:
        folder_info = service.files().get(
            fileId=FOLDER_ID,
            fields='id,name,driveId,parents,capabilities,permissionIds,owners',
            supportsAllDrives=True
        ).execute()

        print(f"📁 Folder Name: {folder_info.get('name')}")
        print(f"📁 Folder ID: {folder_info.get('id')}")
        print(f"📁 Drive ID: {folder_info.get('driveId', 'None (Regular Drive)')}")
        print(f"📁 Parents: {folder_info.get('parents', 'None')}")
        print(f"📁 Capabilities: {folder_info.get('capabilities', {})}")

        # Check if we can write to this folder
        can_add_children = folder_info.get('capabilities', {}).get('canAddChildren', False)
        print(f"📁 Can Add Files: {can_add_children}")

        return folder_info.get('driveId') is not None, can_add_children

    except Exception as e:
        print(f"❌ Error checking folder: {str(e)}")
        return False, False

def main():
    print("🚀 Starting Google Drive upload process...")
    print(f"🔐 Using {AUTH_METHOD} authentication")

    service = authenticate()
    if service is None:
        print("❌ Authentication failed")
        return

    print("✅ Authentication successful")

    # Check folder details
    is_shared_drive, can_add_files = check_folder_details(service)

    if not can_add_files:
        print("❌ Cannot add files to this folder. Check permissions.")
        return

    if not is_shared_drive and AUTH_METHOD == 'service_account':
        print("❌ Service accounts cannot upload to regular Drive folders.")
        print("   Please use OAuth authentication or move folder to Shared Drive.")
        return
    elif not is_shared_drive:
        print("✅ Using OAuth - can upload to regular Drive folders")

    existing_files = get_existing_files(service)
    print(f"📋 Found {len(existing_files)} existing files in target folder")

    upload_bat_files(service, existing_files)

if __name__ == '__main__':
    main()
