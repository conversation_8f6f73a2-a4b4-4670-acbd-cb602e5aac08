from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import os

# === CONFIG ===
SERVICE_ACCOUNT_FILE = r"D:\Automation Files\ROasis YGN Data Google Drive Upload\elite-pointer-465509-m8-7c938d418294.json"
FOLDER_ID = '14tj48mOu51vjRTb53U_61sbGExsFKkZB'
LOCAL_FOLDER_PATH = r"D:\Automation Files\ROasis YGN Data Google Drive Upload"
SCOPES = ['https://www.googleapis.com/auth/drive']

def authenticate():
    creds = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)
    return build('drive', 'v3', credentials=creds)

def get_existing_files(service):
    files_in_drive = {}
    page_token = None
    while True:
        response = service.files().list(
            q=f"'{FOLDER_ID}' in parents and trashed=false",
            fields="nextPageToken, files(id, name)",
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            corpora='allDrives',
            pageToken=page_token
        ).execute()

        for file in response.get('files', []):
            files_in_drive[file['name']] = file['id']
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
    return files_in_drive

def upload_bat_files(service, existing_files):
    for filename in os.listdir(LOCAL_FOLDER_PATH):
        if filename.endswith(".bak") and filename not in existing_files:
            filepath = os.path.join(LOCAL_FOLDER_PATH, filename)
            print(f"Uploading: {filename}")
            file_metadata = {
                'name': filename,
                'parents': [FOLDER_ID]
            }
            media = MediaFileUpload(filepath, resumable=True)
            try:
                result = service.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id',
                    supportsAllDrives=True
                ).execute()
                print(f"✅ Uploaded: {filename} (ID: {result.get('id')})")
            except Exception as e:
                print(f"❌ Failed to upload {filename}: {str(e)}")
                # Try to get more info about the folder
                try:
                    folder_info = service.files().get(
                        fileId=FOLDER_ID,
                        fields='id,name,driveId,parents',
                        supportsAllDrives=True
                    ).execute()
                    print(f"Folder info: {folder_info}")
                except Exception as folder_error:
                    print(f"Could not get folder info: {folder_error}")
        elif filename.endswith(".bak"):
            print(f"⏭️ Skipped (already exists): {filename}")
        else:
            print(f"⏭️ Skipped (not a .bak file): {filename}")

def verify_folder_access(service):
    """Verify that we can access the target folder and check if it's in a Shared Drive"""
    try:
        folder_info = service.files().get(
            fileId=FOLDER_ID,
            fields='id,name,driveId,parents,capabilities',
            supportsAllDrives=True
        ).execute()

        print(f"📁 Target folder: {folder_info.get('name')} (ID: {folder_info.get('id')})")

        if folder_info.get('driveId'):
            print(f"✅ Folder is in Shared Drive: {folder_info.get('driveId')}")
            return True
        else:
            print("⚠️ Folder appears to be in regular Drive (not Shared Drive)")
            print("This might cause upload issues with service accounts")
            return False

    except Exception as e:
        print(f"❌ Cannot access folder {FOLDER_ID}: {str(e)}")
        return False

def main():
    print("🚀 Starting Google Drive upload process...")
    service = authenticate()
    print("✅ Authentication successful")

    # Verify folder access
    if not verify_folder_access(service):
        print("❌ Folder verification failed. Please check folder ID and permissions.")
        return

    existing_files = get_existing_files(service)
    print(f"📋 Found {len(existing_files)} existing files in Drive folder")

    upload_bat_files(service, existing_files)

if __name__ == '__main__':
    main()
