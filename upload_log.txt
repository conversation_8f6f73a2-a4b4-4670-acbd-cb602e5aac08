2025-07-12 16:19:50,431 - INFO - [2025-07-12 16:19:50] MAIN PROCESS STARTED - Script: D:\Automation Files\ROasis YGN Data Google Drive Upload\upload_to_drive.py
2025-07-12 16:19:50,434 - INFO - [2025-07-12 16:19:50] AUTHENTICATION STARTED - Method: oauth
2025-07-12 16:19:50,437 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-12 16:19:50,440 - INFO - [2025-07-12 16:19:50] AUTHENTICATION SUCCESSFUL - Method: oauth
2025-07-12 16:19:50,440 - INFO - [2025-07-12 16:19:50] FOLDER VERIFICATION STARTED - Target folder ID: 14tj48mOu51vjRTb53U_61sbGExsFKkZB
2025-07-12 16:19:51,239 - INFO - [2025-07-12 16:19:51] FOLDER VERIFICATION SUCCESSFUL - <PERSON><PERSON><PERSON> can upload to regular Drive folder
2025-07-12 16:19:51,240 - INFO - [2025-07-12 16:19:51] SCANNING EXISTING FILES - Target folder ID: 14tj48mOu51vjRTb53U_61sbGExsFKkZB
2025-07-12 16:19:51,762 - INFO - [2025-07-12 16:19:51] EXISTING FILES FOUND - Count: 86 files
2025-07-12 16:19:51,763 - INFO - [2025-07-12 16:19:51] UPLOAD SESSION STARTED - Scanning directory: D:\Automation Files\ROasis YGN Data Google Drive Upload
2025-07-12 16:19:51,764 - INFO - [2025-07-12 16:19:51] FILE SKIPPED - File: elite-pointer-465509-m8-7c938d418294.json (not a .bak file)
2025-07-12 16:19:51,767 - INFO - [2025-07-12 16:19:51] FILE SKIPPED - File: GoogleDriveUploadKey.json (not a .bak file)
2025-07-12 16:19:51,768 - INFO - [2025-07-12 16:19:51] FILE SKIPPED - File: GoogleUploadOAuthClient.json (not a .bak file)
2025-07-12 16:19:51,769 - INFO - [2025-07-12 16:19:51] UPLOAD STARTED - File: ROPTN20250704143001.bak | Size: 21.42 MB (22,461,440 bytes)
2025-07-12 16:19:51,769 - INFO - [2025-07-12 16:19:51] CREATING MEDIA UPLOAD - File: ROPTN20250704143001.bak
2025-07-12 16:19:51,836 - INFO - [2025-07-12 16:19:51] SENDING TO GOOGLE DRIVE - File: ROPTN20250704143001.bak
2025-07-12 16:20:01,204 - INFO - [2025-07-12 16:20:01] UPLOAD COMPLETED - File: ROPTN20250704143001.bak | Drive ID: 12ZgxnL2sSp8GEzCZ2xAna8tThkQe4Zf_ | Duration: 9.43 seconds
2025-07-12 16:20:01,205 - INFO - UPLOAD SUMMARY: {
  "timestamp": "2025-07-12 16:20:01",
  "filename": "ROPTN20250704143001.bak",
  "file_size_mb": 21.42,
  "file_size_bytes": 22461440,
  "duration_seconds": 9.43,
  "duration_formatted": "9.43 seconds",
  "status": "SUCCESS",
  "local_path": "D:\\Automation Files\\ROasis YGN Data Google Drive Upload\\ROPTN20250704143001.bak",
  "target_folder_id": "14tj48mOu51vjRTb53U_61sbGExsFKkZB",
  "error_message": ""
}
2025-07-12 16:20:01,206 - INFO - [2025-07-12 16:20:01] FILE SKIPPED - File: token.pickle (not a .bak file)
2025-07-12 16:20:01,207 - INFO - [2025-07-12 16:20:01] FILE SKIPPED - File: upload_log.txt (not a .bak file)
2025-07-12 16:20:01,207 - INFO - [2025-07-12 16:20:01] FILE SKIPPED - File: upload_to_drive.bat (not a .bak file)
2025-07-12 16:20:01,208 - INFO - [2025-07-12 16:20:01] FILE SKIPPED - File: upload_to_drive.py (not a .bak file)
2025-07-12 16:20:01,209 - INFO - [2025-07-12 16:20:01] UPLOAD SESSION COMPLETED - Summary: {
  "session_duration": "9.44 seconds",
  "total_files_processed": 1,
  "successful_uploads": 1,
  "failed_uploads": 0,
  "skipped_files": 0,
  "session_end_time": "2025-07-12 16:20:01"
}
2025-07-12 16:20:01,211 - INFO - [2025-07-12 16:20:01] MAIN PROCESS COMPLETED - Total duration: 10.78 seconds
